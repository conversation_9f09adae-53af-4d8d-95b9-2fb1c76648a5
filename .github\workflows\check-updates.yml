#
# Copyright (c) 2019-2021 P3TERX <https://p3terx.com>
#
# This is free software, licensed under the MIT License.
# See /LICENSE for more information.
#
# https://github.com/P3TERX/Actions-OpenWrt
# File: .github/workflows/update-checker.yml
# Description: Source code update checker
#

name: Update Checker

env:
  REPO_NAME: immortalwrt/immortalwrt
  BUILD_VER: 24.10

on:
  workflow_dispatch:
  schedule:
    - cron: '23 1 * * *'

jobs:
  check:
    runs-on: ubuntu-latest

    steps:

    - name: Check Updates
      run: |
        CURRENT_TAG=$(curl -H "Authorization: Bearer ${{ secrets.MY_SECRETS_TOKEN }}" \
        -sX GET "https://api.github.com/repos/$GITHUB_REPOSITORY/releases/latest" | awk -F '"' '/tag_name/{print $4}')
        REPO_TAG=$(curl -H "Authorization: Bearer ${{ secrets.MY_SECRETS_TOKEN }}" \
        -sX GET "https://api.github.com/repos/$REPO_NAME/tags" | awk -F '"' '/name/{print $4}' | grep $BUILD_VER | head -n 1)
        if [[ $CURRENT_TAG =~ "rc" ]]; then
                CURRENT_VER=$(echo "$CURRENT_TAG" | awk -F'-' '{print $1 "-" $2}')
        else
                CURRENT_VER=$(echo "$CURRENT_TAG" | awk -F'-' '{print $1}')
        fi
        echo "Current Version: $CURRENT_VER"
        echo "Repo Version: $REPO_TAG"
        echo "CURRENT_VER=$CURRENT_VER" >> $GITHUB_ENV
        echo "REPO_TAG=$REPO_TAG" >> $GITHUB_ENV
        echo "HAS_UPDATE=false" >> $GITHUB_ENV
        if [ "$CURRENT_VER" != "$REPO_TAG" ]; then
          echo "Found Updates, Start Build..."
          echo "HAS_UPDATE=true" >> $GITHUB_ENV
        else
          echo "No Updates, Keep cache alive..."
        fi

    - name: Trigger build
      if: ${{ env.HAS_UPDATE == 'true' }}
      uses: peter-evans/repository-dispatch@main
      with:
        token: ${{ secrets.MY_SECRETS_TOKEN }}
        event-type: Source Code Update
        client-payload: '{"clean_cache": "false"}'

    - name: Keep cache alive
      if: ${{ env.HAS_UPDATE == 'false' }}
      run: |
        echo "No updates found. Triggering minimal build to keep cache alive..."

    - name: Trigger cache keep-alive build
      if: ${{ env.HAS_UPDATE == 'false' }}
      uses: peter-evans/repository-dispatch@main
      with:
        token: ${{ secrets.MY_SECRETS_TOKEN }}
        event-type: Keep Cache Alive
        client-payload: '{"clean_cache": "false", "repo_tag": "${{ env.REPO_TAG }}"}'

    - name: Delete workflow runs
      uses: Mattraks/delete-workflow-runs@main
      with:
        token: ${{ github.token }}
        retain_days: 1
        keep_minimum_runs: 3
