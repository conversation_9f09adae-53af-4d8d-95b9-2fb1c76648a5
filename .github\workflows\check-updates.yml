#
# Copyright (c) 2019-2021 P3TERX <https://p3terx.com>
#
# This is free software, licensed under the MIT License.
# See /LICENSE for more information.
#
# https://github.com/P3TERX/Actions-OpenWrt
# File: .github/workflows/update-checker.yml
# Description: Source code update checker
#

name: Update Checker

env:
  REPO_NAME: immortalwrt/immortalwrt
  BUILD_VER: 24.10

on:
  workflow_dispatch:
  schedule:
    - cron: '23 1 * * *'

jobs:
  keep-cache-alive:
    runs-on: ubuntu-22.04
    steps:
    - name: Checkout
      uses: actions/checkout@main

    - name: Keep cache alive
      uses: klever1988/cachewrtbuild@main
      with:
        ccache: 'true'
        prefix: ${{ github.workspace }}/openwrt
        clean: 'false'

  check:
    runs-on: ubuntu-latest
    needs: keep-cache-alive

    steps:

    - name: Check Updates
      run: |
        CURRENT_TAG=$(curl -H "Authorization: Bearer ${{ secrets.MY_SECRETS_TOKEN }}" \
        -sX GET "https://api.github.com/repos/$GITHUB_REPOSITORY/releases/latest" | awk -F '"' '/tag_name/{print $4}')
        REPO_TAG=$(curl -H "Authorization: Bearer ${{ secrets.MY_SECRETS_TOKEN }}" \
        -sX GET "https://api.github.com/repos/$REPO_NAME/tags" | awk -F '"' '/name/{print $4}' | grep $BUILD_VER | head -n 1)
        if [[ $CURRENT_TAG =~ "rc" ]]; then
                CURRENT_VER=$(echo "$CURRENT_TAG" | awk -F'-' '{print $1 "-" $2}')
        else
                CURRENT_VER=$(echo "$CURRENT_TAG" | awk -F'-' '{print $1}')
        fi
        echo "Current Version: $CURRENT_VER"
        echo "Repo Version: $REPO_TAG"
        echo "CURRENT_VER=$CURRENT_VER" >> $GITHUB_ENV
        echo "REPO_TAG=$REPO_TAG" >> $GITHUB_ENV
        if [ "$CURRENT_VER" == "$REPO_TAG" ]; then
          echo "No Updates"
          exit 0
        fi
        echo "Found Updates, Start Build..."

    - name: Trigger build
      if: ${{ env.CURRENT_VER != env.REPO_TAG }}
      uses: peter-evans/repository-dispatch@main
      with:
        token: ${{ secrets.MY_SECRETS_TOKEN }}
        event-type: Source Code Update

    - name: Delete workflow runs
      uses: Mattraks/delete-workflow-runs@main
      with:
        token: ${{ github.token }}
        retain_days: 1
        keep_minimum_runs: 3
