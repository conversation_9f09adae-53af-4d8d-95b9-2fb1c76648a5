#!/bin/bash
#
# Copyright (c) 2019-2020 P3TERX <https://p3terx.com>
#
# This is free software, licensed under the MIT License.
# See /LICENSE for more information.
#
# https://github.com/P3TERX/Actions-OpenWrt
# File name: diy-part1.sh
# Description: OpenWrt DIY script part 1 (Before Update feeds)
#

# Uncomment a feed source
#sed -i 's/^#\(.*helloworld\)/\1/' feeds.conf.default

# Remove git revision
curl https://raw.githubusercontent.com/immortalwrt/immortalwrt/openwrt-24.10/feeds.conf.default -o feeds.conf.default

# 优化 Git 配置以加速克隆
git config --global http.postBuffer 524288000
git config --global http.maxRequestBuffer 100M
git config --global core.compression 0
git config --global protocol.version 2

# Add a feed source
#echo 'src-git helloworld https://github.com/fw876/helloworld' >>feeds.conf.default
#echo 'src-git passwall https://github.com/xiaorouji/openwrt-passwall' >>feeds.conf.default
echo 'src-git fanx https://github.com/FanxJK/openwrt-packages' >> feeds.conf.default

# 设置编译环境变量以优化性能
export FORCE_UNSAFE_CONFIGURE=1
export MAKEFLAGS="-j$(nproc)"
