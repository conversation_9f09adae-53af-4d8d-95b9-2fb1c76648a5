#!/bin/bash
#
# OpenWrt 编译优化脚本
# 用于进一步优化编译速度和性能
#

set -e

echo "开始优化 OpenWrt 编译环境..."

# 设置编译优化环境变量
export FORCE_UNSAFE_CONFIGURE=1
export MAKEFLAGS="-j$(nproc)"
export CCACHE_DIR="/tmp/.ccache"
export CCACHE_MAXSIZE="2G"
export CCACHE_COMPRESS="1"
export CCACHE_COMPRESSLEVEL="6"

# 优化系统设置
echo "优化系统设置..."
# 增加文件描述符限制
ulimit -n 65536
# 优化内存使用
echo 3 > /proc/sys/vm/drop_caches 2>/dev/null || true
# 设置 I/O 调度器为 deadline（如果可用）
echo deadline > /sys/block/sda/queue/scheduler 2>/dev/null || true

# 优化 Git 设置
echo "优化 Git 配置..."
git config --global http.postBuffer 524288000
git config --global http.maxRequestBuffer 100M
git config --global core.compression 0
git config --global protocol.version 2
git config --global core.preloadindex true
git config --global core.fscache true
git config --global gc.auto 0

# 设置 ccache
echo "配置 ccache..."
ccache --set-config=cache_dir=/tmp/.ccache
ccache --set-config=max_size=2G
ccache --set-config=compression=true
ccache --set-config=compression_level=6
ccache --set-config=hash_dir=false
ccache --set-config=sloppiness=file_macro,locale,time_macros
ccache --zero-stats

# 优化 make 设置
echo "优化 make 配置..."
export MAKEFLAGS="-j$(nproc) --load-average=$(nproc)"

# 创建临时目录用于加速编译
echo "创建临时编译目录..."
mkdir -p /tmp/openwrt-build
export TMPDIR=/tmp/openwrt-build

echo "编译环境优化完成！"
echo "CPU 核心数: $(nproc)"
echo "可用内存: $(free -h | awk '/^Mem:/ {print $7}')"
echo "ccache 配置:"
ccache --show-config | grep -E "(cache_dir|max_size|compression)"

# 显示优化后的环境变量
echo "优化后的环境变量:"
echo "MAKEFLAGS=$MAKEFLAGS"
echo "CCACHE_DIR=$CCACHE_DIR"
echo "CCACHE_MAXSIZE=$CCACHE_MAXSIZE"
