#
# 快速编译 OpenWrt - 优化版本
# 专注于速度优化的构建流程
#

name: Build OpenWrt (Fast)

on:
  repository_dispatch:
  workflow_dispatch:
    inputs:
      clean_cache:
        description: 'Clean cache'
        required: false
        default: "false"
      use_fast_config:
        description: 'Use fast config (minimal packages)'
        required: false
        default: "true"

env:
  REPO_NAME: immortalwrt/immortalwrt
  BUILD_VER: "24.10"
  FEEDS_CONF: feeds.conf.default
  CONFIG_FILE: .config.fast
  DIY_P1_SH: diy-part1.sh
  DIY_P2_SH: diy-part2.sh
  UPLOAD_BIN_DIR: false
  UPLOAD_FIRMWARE: true
  UPLOAD_RELEASE: true
  TZ: Asia/Shanghai

jobs:
  build:
    runs-on: ubuntu-24.04
    permissions:
      actions: write
      contents: write

    steps:

    - name: Initialization environment
      env:
        DEBIAN_FRONTEND: noninteractive
      run: |
        # 快速清理，只保留必要步骤
        sudo rm -rf /usr/share/dotnet /usr/local/lib/android /opt/ghc &
        sudo rm -rf /usr/local/share/boost /usr/local/graalvm &
        wait
        
        sudo docker image prune --all --force
        
        # 使用阿里云镜像源加速
        sudo sed -i 's|http://archive.ubuntu.com|http://mirrors.aliyun.com|g' /etc/apt/sources.list
        sudo sed -i 's|http://security.ubuntu.com|http://mirrors.aliyun.com|g' /etc/apt/sources.list
        
        sudo apt-get -qq update
        # 只安装必要的依赖
        sudo apt-get -qq install -y --no-install-recommends \
          build-essential ccache clang cmake ninja-build git curl wget \
          autoconf automake binutils bison flex gawk gettext \
          gcc-multilib g++-multilib libssl-dev zlib1g-dev \
          python3 python3-pip unzip rsync
        
        sudo timedatectl set-timezone "$TZ"
        sudo mkdir -p /workdir
        sudo chown $USER:$GROUPS /workdir

    - name: Checkout
      uses: actions/checkout@main

    - name: Clone source code
      working-directory: /workdir
      run: |
        REPO_URL="https://github.com/$REPO_NAME.git"
        REPO_TAG=$(curl -H "Authorization: Bearer ${{ secrets.MY_SECRETS_TOKEN }}" \
        -sX GET "https://api.github.com/repos/$REPO_NAME/tags" | awk -F '"' '/name/{print $4}' | grep $BUILD_VER | head -n 1)
        echo "REPO_TAG=$REPO_TAG" >> $GITHUB_ENV
        echo "Build Version: $REPO_TAG"
        
        # 快速克隆
        git config --global http.postBuffer 524288000
        git config --global core.compression 0
        git clone $REPO_URL -b $REPO_TAG --depth=1 --single-branch openwrt
        
        ln -sf /workdir/openwrt $GITHUB_WORKSPACE/openwrt

    - name: Cache
      uses: klever1988/cachewrtbuild@main
      with:
        ccache: 'true'
        mixkey: 'x86_64-fast'
        prefix: ${{ github.workspace }}/openwrt
        clean: ${{ github.event.inputs.clean_cache }}

    - name: Optimize build environment
      run: |
        chmod +x optimize-build.sh
        source ./optimize-build.sh

    - name: Load custom feeds
      run: |
        [ -e $FEEDS_CONF ] && mv $FEEDS_CONF openwrt/feeds.conf.default
        chmod +x $DIY_P1_SH
        cd openwrt
        $GITHUB_WORKSPACE/$DIY_P1_SH

    - name: Update and install feeds
      run: |
        cd openwrt
        ./scripts/feeds update -a -j$(nproc)
        ./scripts/feeds install -a -j$(nproc)

    - name: Load custom configuration
      run: |
        [ -e files ] && mv files openwrt/files
        if [ "${{ github.event.inputs.use_fast_config }}" = "true" ]; then
          [ -e .config.fast ] && mv .config.fast openwrt/.config
        else
          [ -e .config ] && mv .config openwrt/.config
        fi
        chmod +x $DIY_P2_SH
        cd openwrt
        $GITHUB_WORKSPACE/$DIY_P2_SH

    - name: Download packages
      run: |
        cd openwrt
        make defconfig
        make download -j$(nproc) V=s DOWNLOAD_ATTEMPTS=3

    - name: Compile firmware
      run: |
        cd openwrt
        echo "开始编译，使用 $(nproc) 个线程"
        
        # 显示 ccache 统计
        ccache --show-stats
        
        # 优化编译
        export FORCE_UNSAFE_CONFIGURE=1
        if ! make -j$(nproc) V=w; then
          echo "并行编译失败，尝试单线程编译..."
          make -j1 V=s
        fi
        
        ccache --show-stats
        echo "COMPILE_STATUS=success" >> $GITHUB_ENV

    - name: Organize files
      run: |
        cd openwrt/bin/targets/*/*
        rm -rf packages
        echo "FIRMWARE=$PWD" >> $GITHUB_ENV

    - name: Upload firmware
      uses: actions/upload-artifact@main
      with:
        name: OpenWrt_firmware_fast_$(date +"%Y%m%d%H%M")
        path: ${{ env.FIRMWARE }}

    - name: Create release
      uses: softprops/action-gh-release@v2
      if: env.UPLOAD_RELEASE == 'true'
      env:
        GITHUB_TOKEN: ${{ github.token }}
      with:
        tag_name: fast-build-$(date +"%Y%m%d%H%M")
        name: OpenWrt Fast Build $(date +"%Y-%m-%d %H:%M")
        files: ${{ env.FIRMWARE }}/*
