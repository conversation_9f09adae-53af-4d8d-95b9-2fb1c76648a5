CONFIG_TARGET_x86=y
CONFIG_TARGET_x86_64=y
CONFIG_TARGET_x86_64_DEVICE_generic=y
CONFIG_DEVEL=y
CONFIG_CCACHE=y
# 编译优化选项
CONFIG_KERNEL_BUILD_DOMAIN="buildhost"
CONFIG_KERNEL_BUILD_USER="builder"
# 启用更多编译优化
CONFIG_TARGET_OPTIMIZATION="-O2 -pipe -march=x86-64"
CONFIG_USE_MOLD=y

# 基础网络库
CONFIG_LIBCURL_COOKIES=y
CONFIG_LIBCURL_CRYPTO_AUTH=y
CONFIG_LIBCURL_FILE=y
CONFIG_LIBCURL_FTP=y
CONFIG_LIBCURL_HTTP=y
CONFIG_LIBCURL_NGHTTP2=y
CONFIG_LIBCURL_OPENSSL=y
CONFIG_LIBCURL_PROXY=y
CONFIG_LIBCURL_TFTP=y
CONFIG_LIBCURL_THREADED_RESOLVER=y
CONFIG_LIBCURL_TLS_SRP=y
CONFIG_LIBCURL_UNIX_SOCKETS=y

# 基础包
CONFIG_PACKAGE_bash=y
CONFIG_PACKAGE_ca-certificates=y
CONFIG_PACKAGE_curl=y
CONFIG_PACKAGE_ip-full=y
CONFIG_PACKAGE_iptables-nft=y
CONFIG_PACKAGE_kmod-ipt-core=y
CONFIG_PACKAGE_kmod-nf-ipt=y
CONFIG_PACKAGE_kmod-nft-compat=y
CONFIG_PACKAGE_libcurl=y
CONFIG_PACKAGE_libnghttp2=y
CONFIG_PACKAGE_libopenssl-conf=y
CONFIG_PACKAGE_libxtables=y
CONFIG_PACKAGE_openssl-util=y
CONFIG_PACKAGE_xtables-nft=y

# 核心 LuCI 应用（精简版）
CONFIG_PACKAGE_luci-app-upnp=y
CONFIG_PACKAGE_luci-i18n-upnp-zh-cn=y
CONFIG_PACKAGE_miniupnpd-nftables=y

# 主题
CONFIG_PACKAGE_luci-theme-argon=y

# 基础工具
CONFIG_PACKAGE_unzip=y
CONFIG_PACKAGE_zlib=y

# 磁盘和文件系统支持
CONFIG_PACKAGE_blkid=y
CONFIG_PACKAGE_lsblk=y
CONFIG_PACKAGE_parted=y
CONFIG_PACKAGE_libparted=y
CONFIG_PARTED_READLINE=y

# 虚拟化支持
CONFIG_PACKAGE_qemu-ga=y
CONFIG_PACKAGE_virtio-console-helper=y

# 镜像格式
CONFIG_QCOW2_IMAGES=y
# CONFIG_TARGET_ROOTFS_EXT4FS is not set
CONFIG_TARGET_ROOTFS_PARTSIZE=512
