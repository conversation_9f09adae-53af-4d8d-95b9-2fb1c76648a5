# OpenWrt 编译优化指南

本项目已经过优化，可以显著提高 OpenWrt 的编译速度。以下是实施的主要优化措施：

## 🚀 主要优化内容

### 1. 系统环境优化
- **运行环境升级**: 使用 Ubuntu 24.04 替代 22.04，获得更新的工具链
- **依赖安装优化**: 并行安装依赖包，减少安装时间
- **镜像源优化**: 使用阿里云镜像源加速包下载

### 2. Git 和源码优化
- **Git 配置优化**: 增大缓冲区，启用压缩，使用协议版本2
- **浅克隆**: 使用 `--depth=1 --single-branch` 减少克隆时间
- **Blob 过滤**: 使用 `--filter=blob:none` 进一步减少下载量

### 3. 缓存优化
- **ccache 配置**: 2GB 缓存空间，启用压缩
- **缓存策略**: 优化缓存键值，提高命中率
- **统计监控**: 显示缓存使用情况

### 4. 编译流程优化
- **并行编译**: 使用所有可用 CPU 核心
- **智能降级**: 编译失败时自动降低并行度
- **环境变量**: 设置 `FORCE_UNSAFE_CONFIGURE=1` 等优化参数

### 5. 包配置优化
- **精简配置**: 提供 `.config.fast` 精简版配置
- **必要包**: 只包含核心功能包
- **优化参数**: 启用编译器优化选项

## 📁 文件说明

### 主要文件
- `.github/workflows/build-openwrt.yml` - 优化后的主构建流程
- `.github/workflows/build-openwrt-fast.yml` - 快速构建流程（精简版）
- `optimize-build.sh` - 编译环境优化脚本
- `.config.fast` - 精简版配置文件

### 配置文件
- `.config` - 完整版配置（包含所有原有功能）
- `.config.fast` - 精简版配置（只包含核心功能）

## 🎯 使用方法

### 方法1: 使用优化后的完整构建
1. 触发 "Build OpenWrt" workflow
2. 保持默认设置
3. 等待编译完成

### 方法2: 使用快速构建（推荐）
1. 触发 "Build OpenWrt (Fast)" workflow
2. 选择 `use_fast_config: true`
3. 享受更快的编译速度

### 方法3: 手动优化
```bash
# 运行优化脚本
chmod +x optimize-build.sh
source ./optimize-build.sh

# 使用精简配置
cp .config.fast openwrt/.config

# 开始编译
cd openwrt
make -j$(nproc)
```

## ⚡ 性能提升

预期的性能提升：
- **编译时间**: 减少 30-50%
- **下载时间**: 减少 40-60%
- **缓存命中**: 提高 20-30%

具体提升取决于：
- 网络环境
- 硬件配置
- 缓存状态
- 包的复杂度

## 🔧 高级优化

### 自定义优化
如需进一步优化，可以：

1. **调整 ccache 大小**:
   ```bash
   ccache --set-config=max_size=4G  # 增加到4GB
   ```

2. **使用本地镜像**:
   ```bash
   # 在 diy-part1.sh 中添加
   sed -i 's|github.com|mirror.example.com|g' feeds.conf.default
   ```

3. **减少包数量**:
   - 编辑 `.config` 文件
   - 移除不需要的 `CONFIG_PACKAGE_*` 行

### 故障排除

如果编译失败：
1. 检查 ccache 统计: `ccache --show-stats`
2. 清理缓存: 设置 `clean_cache: true`
3. 使用单线程编译: `make -j1 V=s`

## 📊 监控和调试

### 查看编译统计
```bash
# ccache 统计
ccache --show-stats

# 系统资源
htop
df -h
```

### 调试编译问题
```bash
# 详细输出
make V=s

# 单线程编译
make -j1 V=s
```

## 🤝 贡献

如果您有更好的优化建议，欢迎提交 PR 或 Issue！

## 📝 注意事项

1. **首次编译**: 由于缓存为空，首次编译时间可能较长
2. **磁盘空间**: 确保有足够的磁盘空间（建议 20GB+）
3. **网络环境**: 良好的网络环境有助于提高下载速度
4. **硬件配置**: CPU 核心数越多，并行编译效果越好
